using Autofac.Core;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.CrossCuttingConcerns.Caching.MultiTenant;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.DependencyResolvers
{
    public class CoreModule : ICoreModule
    {
        public void Load(IServiceCollection serviceCollection)
        {
            // Memory Cache
            serviceCollection.AddMemoryCache();

            // HTTP Context
            serviceCollection.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // Company Context (Multi-tenant)
            serviceCollection.AddScoped<Core.Utilities.Security.CompanyContext.ICompanyContext, Core.Utilities.Security.CompanyContext.CompanyContext>();

            // Cache Configuration
            serviceCollection.AddSingleton<CacheConfiguration>();

            // Cache Key Generator
            serviceCollection.AddSingleton<ICacheKeyGenerator, CacheKeyGenerator>();

            // Multi-Tenant Cache Manager
            serviceCollection.AddSingleton<ICacheManager, MultiTenantCacheManager>();

            // Logging
            serviceCollection.AddSingleton<Stopwatch>();
            serviceCollection.AddSingleton<FileLoggerService>();
            serviceCollection.AddSingleton<PerformanceLoggerService>();
            serviceCollection.AddSingleton<ILogService, FileLoggerService>();
        }
    }
}
